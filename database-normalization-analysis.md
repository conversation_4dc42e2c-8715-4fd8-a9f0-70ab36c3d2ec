# Database Normalization Analysis

## Original Data Analysis

The provided CSV contains the following columns:

- ID, ACCOUNT_NO, ACC_SORT_CODE, CREATE_DATE, UPDATE_DATE, doc_type, name, email, AccountType, doc_number

### Issues with Current Structure (Unnormalized):

1. **Redundancy**: Multiple accounts can have the same sort code (********, ********, etc.)
2. **Mixed Data Types**: AccountType contains currency codes (KES, USD) rather than actual account types
3. **No Clear Separation**: Customer information mixed with account information
4. **Potential Anomalies**: Updates to sort codes would require multiple row updates

## Normalization Process

### First Normal Form (1NF)

✅ **Already Achieved**:

- All attributes contain atomic values
- No repeating groups
- Each row is unique (ID is primary key)

### Second Normal Form (2NF)

**Issues to Address**:

- Non-key attributes depend on the full primary key
- Need to separate customer data from account data

### Third Normal Form (3NF)

**Issues to Address**:

- Remove transitive dependencies
- Separate sort codes into their own entity
- Separate document types into their own entity

## Normalized Database Design

### Table 1: Customers

```sql
CREATE TABLE customers (
    customer_id INT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    doc_type_id INT,
    doc_number VARCHAR(50),
    created_date TIMESTAMP,
    updated_date TIMESTAMP,
    FOREIGN KEY (doc_type_id) REFERENCES document_types(doc_type_id)
);
```

### Table 2: Document_Types

```sql
CREATE TABLE document_types (
    doc_type_id INT PRIMARY KEY AUTO_INCREMENT,
    doc_type_name VARCHAR(50) UNIQUE NOT NULL
);
```

### Table 3: Bank_Branches

```sql
CREATE TABLE bank_branches (
    sort_code INT PRIMARY KEY,
    branch_name VARCHAR(100),
    branch_location VARCHAR(100)
);
```

### Table 4: Currency_Types

```sql
CREATE TABLE currency_types (
    currency_code CHAR(3) PRIMARY KEY,
    currency_name VARCHAR(50),
    currency_symbol VARCHAR(5)
);
```

### Table 5: Accounts

```sql
CREATE TABLE accounts (
    account_id INT PRIMARY KEY,
    account_no BIGINT UNIQUE NOT NULL,
    customer_id INT NOT NULL,
    sort_code INT NOT NULL,
    currency_code CHAR(3) NOT NULL,
    created_date TIMESTAMP,
    updated_date TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
    FOREIGN KEY (sort_code) REFERENCES bank_branches(sort_code),
    FOREIGN KEY (currency_code) REFERENCES currency_types(currency_code)
);
```

## Data Migration

### Document Types Data:

```sql
INSERT INTO document_types (doc_type_name) VALUES
('NationalID'), ('MilitaryID'), ('CR12'), ('Passport');
```

### Currency Types Data:

```sql
INSERT INTO currency_types (currency_code, currency_name, currency_symbol) VALUES
('KES', 'Kenyan Shilling', 'KSh'),
('USD', 'US Dollar', '$');
```

### Bank Branches Data:

```sql
INSERT INTO bank_branches (sort_code, branch_name) VALUES
(********, 'Branch A'),
(********, 'Branch B'),
(********, 'Branch C'),
(********, 'Branch D');
```

### Customers Data:

```sql
INSERT INTO customers (customer_id, name, email, doc_type_id, doc_number, created_date, updated_date) VALUES
(1, 'John Doe', '<EMAIL>', 1, '********', '2021-06-14 11:31:53', '2021-06-14 11:31:53'),
(2, 'James Smith', '<EMAIL>', 2, '********', '2016-11-29 23:25:10', '2016-11-29 23:25:10'),
-- ... (continue for all customers)
```

### Accounts Data:

```sql
INSERT INTO accounts (account_id, account_no, customer_id, sort_code, currency_code, created_date, updated_date) VALUES
(********, **********, 1, ********, 'KES', '2021-06-14 11:31:53', '2021-06-14 11:31:53'),
(7983757, **********, 2, ********, 'KES', '2016-11-29 23:25:10', '2016-11-29 23:25:10'),
-- ... (continue for all accounts)
```

## Benefits of Normalization

1. **Eliminates Redundancy**: Sort codes and document types stored once
2. **Improves Data Integrity**: Foreign key constraints prevent invalid data
3. **Reduces Storage**: Less duplicate data
4. **Easier Maintenance**: Changes to branch info only need one update
5. **Better Query Performance**: Proper indexing on normalized tables
6. **Scalability**: Easy to add new document types, currencies, or branches

## Relationships Summary

- **Customers** (1) → (M) **Accounts** (One customer can have multiple accounts)
- **Document_Types** (1) → (M) **Customers** (One document type used by multiple customers)
- **Bank_Branches** (1) → (M) **Accounts** (One branch serves multiple accounts)
- **Currency_Types** (1) → (M) **Accounts** (One currency used by multiple accounts)

This normalized structure follows 3NF principles and eliminates the identified issues in the original table.

## ER Diagram (Text Representation)

```
┌─────────────────────────────────────┐
│            DOCUMENT_TYPES           │
├─────────────────────────────────────┤
│ PK  doc_type_id      INT            │
│     doc_type_name    VARCHAR(50) UK │
└─────────────────────────────────────┘
                    │
                    │ 1:M
                    ▼
┌─────────────────────────────────────┐
│              CUSTOMERS              │
├─────────────────────────────────────┤
│ PK  customer_id      INT            │
│     name             VARCHAR(100)   │
│     email            VARCHAR(100) UK│
│ FK  doc_type_id      INT            │
│     doc_number       VARCHAR(50)    │
│     created_date     TIMESTAMP      │
│     updated_date     TIMESTAMP      │
└─────────────────────────────────────┘
                    │
                    │ 1:M
                    ▼
┌─────────────────────────────────────┐
│              ACCOUNTS               │
├─────────────────────────────────────┤
│ PK  account_id       INT            │
│     account_no       BIGINT UK      │
│ FK  customer_id      INT            │
│ FK  sort_code        INT            │
│ FK  currency_code    CHAR(3)        │
│     created_date     TIMESTAMP      │
│     updated_date     TIMESTAMP      │
└─────────────────────────────────────┘
          ▲                    ▲
          │ M:1                │ M:1
          │                    │
┌─────────────────────┐ ┌─────────────────────┐
│    BANK_BRANCHES    │ │   CURRENCY_TYPES    │
├─────────────────────┤ ├─────────────────────┤
│ PK sort_code   INT  │ │ PK currency_code    │
│    branch_name      │ │    CHAR(3)          │
│    VARCHAR(100)     │ │    currency_name    │
│    branch_location  │ │    VARCHAR(50)      │
│    VARCHAR(100)     │ │    currency_symbol  │
└─────────────────────┘ │    VARCHAR(5)       │
                        └─────────────────────┘

Legend:
PK = Primary Key
FK = Foreign Key
UK = Unique Key
1:M = One-to-Many relationship
M:1 = Many-to-One relationship
```

## Relationship Details

1. **DOCUMENT_TYPES → CUSTOMERS** (1:M)

   - One document type can be used by many customers
   - Each customer has exactly one document type

2. **CUSTOMERS → ACCOUNTS** (1:M)

   - One customer can have multiple accounts
   - Each account belongs to exactly one customer

3. **BANK_BRANCHES → ACCOUNTS** (1:M)

   - One bank branch can serve many accounts
   - Each account is associated with exactly one branch (sort code)

4. **CURRENCY_TYPES → ACCOUNTS** (1:M)
   - One currency type can be used by many accounts
   - Each account is denominated in exactly one currency
